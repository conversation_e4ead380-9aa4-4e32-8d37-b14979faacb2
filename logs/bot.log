2025-06-18 11:49:43,999 - __main__ - WARNING - Slow command execution: watchlist took 6.10s
2025-06-18 12:06:07,817 - handlers.discord.market.market_commands - WARNING - Interaction already expired for market command by fantastic_beagle_96376
2025-06-18 15:30:54,244 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d4f0821c576a93914d8440c65f7a16a7f0252d3ac25b8b2f1bdb7b5c487450cd
2025-06-18 15:30:54,247 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d4f0821c576a93914d8440c65f7a16a7f0252d3ac25b8b2f1bdb7b5c487450cd (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d4f0821c576a93914d8440c65f7a16a7f0252d3ac25b8b2f1bdb7b5c487450cd)
2025-06-18 16:39:04,683 - __main__ - ERROR - Error updating watchlist in channel 1376101742772748328: 503 Service Unavailable (error code: 0): upstream connect error or disconnect/reset before headers. reset reason: remote connection failure, transport failure reason: immediate connect error: No such file or directory
2025-06-18 22:22:11,314 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ea82e927992e5f8151a8130d737cfbb3e2e1c3cf6111ad305880105b31c3c8ea
2025-06-18 22:22:11,318 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ea82e927992e5f8151a8130d737cfbb3e2e1c3cf6111ad305880105b31c3c8ea (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ea82e927992e5f8151a8130d737cfbb3e2e1c3cf6111ad305880105b31c3c8ea)
2025-06-18 22:25:22,261 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=054603b38759f5b78891bcabd5b5e78e89207d5f3603246e2fd2deaae04a058e
2025-06-18 22:25:22,263 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=054603b38759f5b78891bcabd5b5e78e89207d5f3603246e2fd2deaae04a058e (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=054603b38759f5b78891bcabd5b5e78e89207d5f3603246e2fd2deaae04a058e)
2025-06-20 07:21:11,917 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c9266f09e304d12e2a1ffd988658ac8510e6a55a14e4981ce8bdbaab7c2fafb1
2025-06-20 07:21:11,925 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c9266f09e304d12e2a1ffd988658ac8510e6a55a14e4981ce8bdbaab7c2fafb1 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c9266f09e304d12e2a1ffd988658ac8510e6a55a14e4981ce8bdbaab7c2fafb1)
2025-06-20 13:51:34,337 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=4d047ca960030ba210fda10bf9435792c4f2285e640fca3146b31acb483a41ec
2025-06-20 13:51:34,343 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=4d047ca960030ba210fda10bf9435792c4f2285e640fca3146b31acb483a41ec (Caused by: RequestTimeout: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=4d047ca960030ba210fda10bf9435792c4f2285e640fca3146b31acb483a41ec)
2025-06-20 13:51:35,200 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 71, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 188, in sync_wrapper
    time.sleep(sleep_time)

2025-06-21 14:39:56,679 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=450ecfb1d2889141dad694f69bb8d7b6377b2e256bf2f97596d4713f294a7907
2025-06-21 14:39:56,682 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=450ecfb1d2889141dad694f69bb8d7b6377b2e256bf2f97596d4713f294a7907 (Caused by: RequestTimeout: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=450ecfb1d2889141dad694f69bb8d7b6377b2e256bf2f97596d4713f294a7907)
2025-06-21 14:39:59,160 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 71, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 57, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 383, in _make_request
    self._validate_conn(conn)
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 1017, in _validate_conn
    conn.connect()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 411, in connect
    self.sock = ssl_wrap_socket(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 449, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 493, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
  File "/usr/lib/python3.10/ssl.py", line 513, in wrap_socket
    return self.sslsocket_class._create(
  File "/usr/lib/python3.10/ssl.py", line 1100, in _create
    self.do_handshake()
  File "/usr/lib/python3.10/ssl.py", line 1371, in do_handshake
    self._sslobj.do_handshake()

2025-06-21 21:17:45,575 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c1cac8dfc749089241582d4706e746a50ed7f14b2995ef73494ea9a558d6b61f
2025-06-21 21:17:45,578 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c1cac8dfc749089241582d4706e746a50ed7f14b2995ef73494ea9a558d6b61f (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c1cac8dfc749089241582d4706e746a50ed7f14b2995ef73494ea9a558d6b61f)
2025-06-21 22:24:35,216 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6cddfcc5f6a54076b9db1ce106fd264c5a831b1516db95abf1adb74028427b00
2025-06-21 22:24:35,218 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6cddfcc5f6a54076b9db1ce106fd264c5a831b1516db95abf1adb74028427b00 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6cddfcc5f6a54076b9db1ce106fd264c5a831b1516db95abf1adb74028427b00)
2025-06-22 02:33:06,654 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=562b11e93c7f3c58c2320ab55ea69592254b5f4fe21cd3514828a89ef7e0f6dd
2025-06-22 02:33:06,657 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=562b11e93c7f3c58c2320ab55ea69592254b5f4fe21cd3514828a89ef7e0f6dd (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=562b11e93c7f3c58c2320ab55ea69592254b5f4fe21cd3514828a89ef7e0f6dd)
2025-06-22 02:42:31,553 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=fd4f52740a2efd4904b5169a7ab0d9c63824a8b57d83e4233a87b4605824d73b
2025-06-22 02:42:31,555 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=fd4f52740a2efd4904b5169a7ab0d9c63824a8b57d83e4233a87b4605824d73b (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=fd4f52740a2efd4904b5169a7ab0d9c63824a8b57d83e4233a87b4605824d73b)
2025-06-22 02:43:36,101 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a21fbb6019da9d10d17d01b236810b4a64928aac211f69e5c839fc8aaa712978
2025-06-22 02:43:36,103 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a21fbb6019da9d10d17d01b236810b4a64928aac211f69e5c839fc8aaa712978 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a21fbb6019da9d10d17d01b236810b4a64928aac211f69e5c839fc8aaa712978)
2025-06-22 03:33:43,007 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7dc8c79a7f239ef72956cc89ffa423bf2004d29a864f8a23721a199abd18bdfb
2025-06-22 03:33:43,012 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7dc8c79a7f239ef72956cc89ffa423bf2004d29a864f8a23721a199abd18bdfb (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7dc8c79a7f239ef72956cc89ffa423bf2004d29a864f8a23721a199abd18bdfb)
2025-06-22 04:01:53,261 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=725ba246e28298b8ecbc9a7509d80b8e18a766b112beb79a59fb1b78e4204f13
2025-06-22 04:01:53,263 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=725ba246e28298b8ecbc9a7509d80b8e18a766b112beb79a59fb1b78e4204f13 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=725ba246e28298b8ecbc9a7509d80b8e18a766b112beb79a59fb1b78e4204f13)
2025-06-22 06:12:19,390 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5595bddb59f4f5c6d61ff7576382b23320b2fdb605251139751eec4a72c349bc
2025-06-22 06:12:19,391 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5595bddb59f4f5c6d61ff7576382b23320b2fdb605251139751eec4a72c349bc (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5595bddb59f4f5c6d61ff7576382b23320b2fdb605251139751eec4a72c349bc)
2025-06-22 11:20:17,082 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=dcec25499a8c60a40e9ff3e979ab00aa73f6f83ac5a92d08d73a2d4619f3d5f9
2025-06-22 11:20:17,086 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=dcec25499a8c60a40e9ff3e979ab00aa73f6f83ac5a92d08d73a2d4619f3d5f9 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=dcec25499a8c60a40e9ff3e979ab00aa73f6f83ac5a92d08d73a2d4619f3d5f9)
2025-06-22 11:29:44,375 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6059c434b3a3e3c390e0ffb7d3779177e49f0d947f505a2eaa007440422fb793
2025-06-22 11:29:44,378 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6059c434b3a3e3c390e0ffb7d3779177e49f0d947f505a2eaa007440422fb793 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6059c434b3a3e3c390e0ffb7d3779177e49f0d947f505a2eaa007440422fb793)
2025-06-23 00:40:01,477 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=70a45371e3c8ddeca7ad1fbf567105526a7b00b6dc5144cc9e018e30f5d19307
2025-06-23 00:40:01,481 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=70a45371e3c8ddeca7ad1fbf567105526a7b00b6dc5144cc9e018e30f5d19307 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=70a45371e3c8ddeca7ad1fbf567105526a7b00b6dc5144cc9e018e30f5d19307)
2025-06-23 07:50:16,879 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3edc54c9e4cce79e9c909dee232213c641c141e35de9e22e46c61a48c1cd64b7
2025-06-23 07:50:16,881 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3edc54c9e4cce79e9c909dee232213c641c141e35de9e22e46c61a48c1cd64b7 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3edc54c9e4cce79e9c909dee232213c641c141e35de9e22e46c61a48c1cd64b7)
2025-06-23 08:14:20,728 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=27424ae9f0a9eb63403549bec754b396fac435466441dab43f7b29421bc3606f
2025-06-23 08:14:20,731 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=27424ae9f0a9eb63403549bec754b396fac435466441dab43f7b29421bc3606f (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=27424ae9f0a9eb63403549bec754b396fac435466441dab43f7b29421bc3606f)
2025-06-23 17:06:11,556 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 14.7s behind.
2025-06-23 17:06:11,792 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 15.0s behind.
2025-06-24 08:10:48,767 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/discord/ext/tasks/__init__.py", line 246, in _loop
    await self.coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 375, in watchlist_updater
    embed_content = format_watchlist_message(watchlist_data)
  File "/root/chartfix/utils/ui_components.py", line 370, in format_watchlist_message
    return format_price_message(prices)
  File "/root/chartfix/utils/ui_components.py", line 258, in format_price_message
    volume = fetch_volume_data(symbol)
  File "/root/chartfix/utils/ui_components.py", line 391, in fetch_volume_data
    ticker = exchange.fetch_ticker(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4105, in fetch_ticker
    response = self.fapiPublicGetTicker24hr(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 746, in send
    r.content
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 902, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 820, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 572, in stream
    for line in self.read_chunked(amt, decode_content=decode_content):
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 764, in read_chunked
    self._update_chunk_length()
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 694, in _update_chunk_length
    line = self._fp.fp.readline()
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-24 08:10:58,772 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/discord/ext/tasks/__init__.py", line 246, in _loop
    await self.coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 375, in watchlist_updater
    embed_content = format_watchlist_message(watchlist_data)
  File "/root/chartfix/utils/ui_components.py", line 370, in format_watchlist_message
    return format_price_message(prices)
  File "/root/chartfix/utils/ui_components.py", line 258, in format_price_message
    volume = fetch_volume_data(symbol)
  File "/root/chartfix/utils/ui_components.py", line 391, in fetch_volume_data
    ticker = exchange.fetch_ticker(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4105, in fetch_ticker
    response = self.fapiPublicGetTicker24hr(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 746, in send
    r.content
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 902, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 820, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 572, in stream
    for line in self.read_chunked(amt, decode_content=decode_content):
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 764, in read_chunked
    self._update_chunk_length()
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 694, in _update_chunk_length
    line = self._fp.fp.readline()
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-24 08:11:02,490 - utils.ui_components - ERROR - Lỗi khi lấy khối lượng giao dịch cho DOGEUSDT: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT
2025-06-24 11:15:52,347 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=211b9450c7a33b3a0d43bd99061dcf42125edd0d5db2bea5eecb3fbdc332aab7
2025-06-24 11:15:52,350 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=211b9450c7a33b3a0d43bd99061dcf42125edd0d5db2bea5eecb3fbdc332aab7 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=211b9450c7a33b3a0d43bd99061dcf42125edd0d5db2bea5eecb3fbdc332aab7)
2025-06-24 15:03:27,179 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d0073ef18b0bcf03cd93b4228cbe97880c7266ce7efeaf73bef1b7dc6e4e38d1
2025-06-24 15:03:27,181 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d0073ef18b0bcf03cd93b4228cbe97880c7266ce7efeaf73bef1b7dc6e4e38d1 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d0073ef18b0bcf03cd93b4228cbe97880c7266ce7efeaf73bef1b7dc6e4e38d1)
2025-06-24 15:10:49,397 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b6bacd1c1d25a0bdfe351227c9fa4f6ad7000d23a3f7cf54ddb723542d5bb698
2025-06-24 15:10:49,402 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b6bacd1c1d25a0bdfe351227c9fa4f6ad7000d23a3f7cf54ddb723542d5bb698 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b6bacd1c1d25a0bdfe351227c9fa4f6ad7000d23a3f7cf54ddb723542d5bb698)
2025-06-24 15:20:16,526 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=584d5944bc8ff12222add6808cddbfa6be21e49d4ea83a9a112d86d86744f4a4
2025-06-24 15:20:16,532 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=584d5944bc8ff12222add6808cddbfa6be21e49d4ea83a9a112d86d86744f4a4 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=584d5944bc8ff12222add6808cddbfa6be21e49d4ea83a9a112d86d86744f4a4)
2025-06-24 19:00:01,027 - services.market.market_service - ERROR - Error fetching OHLCV data for ETHUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1d&limit=2&symbol=ETHUSDT 503 Service Temporarily Unavailable <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html>
<head><title>503 Service Temporarily Unavailable</title></head>
<body>
<center><h1>503 Service Temporarily Unavailable</h1></center>
 Sorry for the inconvenience.<br/>
Please report this message and include the following information to us.<br/>
Thank you very much!</p>
<table>
<tr>
<td>URL:</td>
<td>http://fapi.binance.com/fapi/v1/klines?interval=1d&amp;limit=2&amp;symbol=ETHUSDT</td>
</tr>
<tr>
<td>Server:</td>
<td>ip-10-118-198-182.ap-northeast-1.compute.internal</td>
</tr>
<tr>
<td>Date:</td>
<td>2025/06/24 19:00:00</td>
</tr>
</table>
<hr/>Powered by Tengine<hr><center>tengine</center>
</body>
</html>
2025-06-24 19:00:01,036 - utils.ui_components - WARNING - Không thể lấy giá mở cửa ngày cho ETHUSDT
2025-06-26 00:56:10,161 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 71, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 57, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-26 00:56:20,162 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 76, in update_all_data
    positions_result = self.trading_service.get_positions()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 333, in get_positions
    positions = self.exchange.fetch_positions()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 10139, in fetch_positions
    return self.fetch_positions_risk(symbols, params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 10309, in fetch_positions_risk
    response = self.fapiPrivateV3GetPositionRisk(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-26 00:56:30,164 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 76, in update_all_data
    positions_result = self.trading_service.get_positions()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 333, in get_positions
    positions = self.exchange.fetch_positions()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 10139, in fetch_positions
    return self.fetch_positions_risk(symbols, params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 10309, in fetch_positions_risk
    response = self.fapiPrivateV3GetPositionRisk(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-26 00:56:40,165 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 99, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 300, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-26 03:38:10,708 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ddbb0c874a3ba04f586be672bcc00da087c14661b5ee021e186e704d83f5ab60
2025-06-26 03:38:10,711 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ddbb0c874a3ba04f586be672bcc00da087c14661b5ee021e186e704d83f5ab60 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ddbb0c874a3ba04f586be672bcc00da087c14661b5ee021e186e704d83f5ab60)
2025-06-26 05:24:33,148 - services.market.market_service - ERROR - Error fetching OHLCV data for BNBUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1d&limit=2&symbol=BNBUSDT
2025-06-26 11:30:33,978 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:31:37,444 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:32:40,013 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:33:42,536 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:34:45,086 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:35:47,636 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:36:50,199 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:37:52,778 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:38:55,371 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:39:57,903 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:41:00,646 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:42:03,185 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:43:05,778 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:44:08,303 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:45:10,936 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:46:13,425 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:47:15,957 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:48:18,492 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:49:21,011 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:50:23,525 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:51:26,095 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:52:28,602 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:53:28,617 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=362b8345ebdbe991edcc84b42d562f4d9cfad923f086669338eb88e35689d963
2025-06-26 11:53:28,620 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=362b8345ebdbe991edcc84b42d562f4d9cfad923f086669338eb88e35689d963 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=362b8345ebdbe991edcc84b42d562f4d9cfad923f086669338eb88e35689d963)
2025-06-26 11:53:33,314 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:54:35,876 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:55:38,406 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:56:41,021 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:57:43,604 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:58:46,121 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:59:48,685 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:00:51,219 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:01:53,806 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:02:56,352 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:03:58,883 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:05:01,489 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:06:06,565 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:07:09,131 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:08:11,697 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:09:14,243 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:11:19,374 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:12:21,942 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:13:24,502 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:14:27,012 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:15:29,653 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:16:33,176 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:17:35,750 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:18:38,385 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:19:41,014 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:20:43,527 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:21:45,870 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:22:48,096 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:23:50,314 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:24:52,534 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:25:54,766 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:26:56,994 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:27:59,233 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:29:01,443 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:30:06,638 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:31:08,865 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:32:11,093 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:33:13,316 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:34:15,541 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:35:17,768 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:36:20,023 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:37:22,263 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:38:24,492 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:39:26,726 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:40:28,963 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:41:31,189 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:42:33,409 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:43:37,011 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:44:39,237 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:45:41,467 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:46:43,683 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:47:45,903 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:48:48,141 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:49:50,365 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:50:52,613 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:51:54,837 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:52:57,062 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:53:59,307 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:55:01,527 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:56:03,757 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:57:06,517 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:58:08,748 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:59:10,979 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:00:13,222 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:01:15,472 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:02:17,685 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:03:19,902 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:04:22,128 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:05:24,352 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:06:26,577 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:07:28,802 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:08:31,025 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:09:33,245 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:10:36,347 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:11:38,583 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:12:40,819 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:13:43,042 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:14:45,260 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:15:47,492 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:16:49,706 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:17:51,924 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:18:54,136 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:19:56,352 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:20:58,561 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:22:00,819 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:23:03,054 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:24:06,923 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:25:06,768 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:26:09,439 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:27:18,597 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:27:23,048 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-06-26 13:27:23,666 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-06-27 03:07:51,925 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 03:08:22,549 - __main__ - WARNING - Slow command execution: watchlist took 8.09s
2025-06-27 03:08:54,468 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 03:09:57,018 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 03:10:33,335 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387991966104686622 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:34,319 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387991965546582158 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:36,180 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387970518245707797 responded with 429. Retrying in 0.39 seconds.
2025-06-27 03:10:37,221 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387960452260298773 responded with 429. Retrying in 0.35 seconds.
2025-06-27 03:10:38,218 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387957935405269075 responded with 429. Retrying in 0.35 seconds.
2025-06-27 03:10:39,343 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387955418705170557 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:40,296 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387950387746574367 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:41,387 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387947869348036688 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:42,417 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387946609282650203 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:43,473 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387945353583792279 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:44,403 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387942838360080526 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:45,389 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387862305374601266 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:46,420 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387840914545967155 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:47,348 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387822038403780752 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:48,363 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387820783249789059 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:49,375 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387809460516556820 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:50,352 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387801907988271218 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:51,304 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387799391326437590 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:52,262 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387796882398646393 responded with 429. Retrying in 0.34 seconds.
2025-06-27 03:10:53,329 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387796882222481459 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:54,348 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387796880121008130 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:55,282 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387796878732562565 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:10:59,499 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 03:11:06,142 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382889353411428374 responded with 429. Retrying in 0.61 seconds.
2025-06-27 03:11:07,532 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382886837584134185 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:08,470 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382884333513211915 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:09,470 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382881803668291655 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:10,453 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382879287056535655 responded with 429. Retrying in 0.33 seconds.
2025-06-27 03:11:11,412 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382876781555744860 responded with 429. Retrying in 0.34 seconds.
2025-06-27 03:11:12,562 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382874259629805658 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:13,532 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382874257821798462 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:14,522 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382872992664522853 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:15,469 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382826441678389379 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:16,461 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382823938014576762 responded with 429. Retrying in 0.31 seconds.
2025-06-27 03:11:17,516 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382816377752260659 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:18,518 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382813858657341531 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:19,463 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382789960662515733 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:20,394 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382774849398771794 responded with 429. Retrying in 0.36 seconds.
2025-06-27 03:11:21,525 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382753457814896702 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:22,472 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382750940301623408 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:23,430 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382748437149384814 responded with 429. Retrying in 0.32 seconds.
2025-06-27 03:11:24,401 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382745912245227751 responded with 429. Retrying in 0.35 seconds.
2025-06-27 03:11:25,609 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382743391040962636 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:26,557 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382735842107330672 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:27,509 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382720741228937267 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:28,458 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382715710320410836 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:29,394 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382713191653445703 responded with 429. Retrying in 0.36 seconds.
2025-06-27 03:11:31,143 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382705641491140712 responded with 429. Retrying in 0.61 seconds.
2025-06-27 03:11:32,513 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382700610733867040 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:33,538 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382695589254533191 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:34,581 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382657841906847788 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:35,535 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382645245350641706 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:36,569 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382632661624815617 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:37,542 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382627639885566023 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:38,564 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382625113480106036 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:39,536 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382622596679602186 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:40,469 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382620101785686119 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:41,407 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382615046571425843 responded with 429. Retrying in 0.35 seconds.
2025-06-27 03:11:42,437 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382607494915952666 responded with 429. Retrying in 0.32 seconds.
2025-06-27 03:11:43,416 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382604992686588004 responded with 429. Retrying in 0.36 seconds.
2025-06-27 03:11:44,406 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382597442503053416 responded with 429. Retrying in 0.35 seconds.
2025-06-27 03:11:45,517 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382579812601827359 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:46,525 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382574792997343292 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:47,495 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382569751468970014 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:48,478 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382562196893990955 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:49,460 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382544593454633092 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:50,408 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382542068651659336 responded with 429. Retrying in 0.34 seconds.
2025-06-27 03:11:51,461 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382539551498702978 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:52,470 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382533253969481829 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:53,411 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382531998874079323 responded with 429. Retrying in 0.34 seconds.
2025-06-27 03:11:54,399 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382519417748852816 responded with 429. Retrying in 0.37 seconds.
2025-06-27 03:11:55,511 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382516899916550315 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:56,578 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382514395434844220 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:57,533 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382511866810728480 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:58,448 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382510604916625490 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:11:59,410 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382486698818015272 responded with 429. Retrying in 0.35 seconds.
2025-06-27 03:12:02,055 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 03:12:04,249 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382381004089720965 responded with 429. Retrying in 0.53 seconds.
2025-06-27 03:12:05,718 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382375969716834334 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:07,568 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382365903047491785 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:08,525 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382338220695752744 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:09,498 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382325650127458399 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:10,514 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382323122656776252 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:11,453 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382320605122003036 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:12,455 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382287908622303297 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:13,460 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382285372343582762 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:14,666 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382265255031345203 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:15,608 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382257704181174414 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:16,565 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382245108229079152 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:17,473 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382242604896354325 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:18,485 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382232525421805599 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:19,410 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382217425902108712 responded with 429. Retrying in 0.34 seconds.
2025-06-27 03:12:20,592 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382204854524448839 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:21,527 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382202331579420755 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:22,479 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382197295122219051 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:23,509 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382192258836861099 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:24,510 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382187225554489394 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:12:26,615 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382182205689696387 responded with 429. Retrying in 0.30 seconds.
2025-06-27 03:38:59,103 - __main__ - WARNING - Slow command execution: watchlist took 7.76s
2025-06-27 07:02:51,586 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/discord/ext/tasks/__init__.py", line 246, in _loop
    await self.coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 375, in watchlist_updater
    embed_content = format_watchlist_message(watchlist_data)
  File "/root/chartfix/utils/ui_components.py", line 370, in format_watchlist_message
    return format_price_message(prices)
  File "/root/chartfix/utils/ui_components.py", line 258, in format_price_message
    volume = fetch_volume_data(symbol)
  File "/root/chartfix/utils/ui_components.py", line 391, in fetch_volume_data
    ticker = exchange.fetch_ticker(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4105, in fetch_ticker
    response = self.fapiPublicGetTicker24hr(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 746, in send
    r.content
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 902, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 820, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 572, in stream
    for line in self.read_chunked(amt, decode_content=decode_content):
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 764, in read_chunked
    self._update_chunk_length()
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 694, in _update_chunk_length
    line = self._fp.fp.readline()
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-27 07:02:52,955 - utils.ui_components - ERROR - Lỗi khi lấy khối lượng giao dịch cho SOLUSDT: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT
2025-06-27 07:27:58,151 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f0afbab99b9cffc7c15aa55905af42995fec18062640971a6df97c7274682449
2025-06-27 07:27:58,172 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f0afbab99b9cffc7c15aa55905af42995fec18062640971a6df97c7274682449 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f0afbab99b9cffc7c15aa55905af42995fec18062640971a6df97c7274682449)
2025-06-28 13:22:22,036 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-06-28 13:27:17,085 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-06-28 13:27:23,818 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5eca6a625c70c4352e6ddd9d0d355565ebefede81bfe174baeea092a08ade456
2025-06-28 13:27:23,822 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5eca6a625c70c4352e6ddd9d0d355565ebefede81bfe174baeea092a08ade456 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5eca6a625c70c4352e6ddd9d0d355565ebefede81bfe174baeea092a08ade456)
2025-06-28 13:53:46,216 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-06-28 13:53:47,005 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-06-28 13:54:36,780 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1384548284865183804 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:54:37,742 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1384497701269540976 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:54:41,138 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1384481470273552404 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:54:43,545 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1384135692766154914 responded with 429. Retrying in 0.48 seconds.
2025-06-28 13:54:44,706 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1384110023248056372 responded with 429. Retrying in 0.31 seconds.
2025-06-28 13:54:45,741 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1384066988929712219 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:54:46,698 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1384025090219573279 responded with 429. Retrying in 0.34 seconds.
2025-06-28 13:54:47,675 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1383959404545708165 responded with 429. Retrying in 0.35 seconds.
2025-06-28 13:54:48,844 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1383826153201991791 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:54:49,932 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1383257656684314645 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:54:50,985 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1383235394354548747 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:54:51,902 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1383234630270062742 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:54:53,635 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382889986906656930 responded with 429. Retrying in 0.39 seconds.
2025-06-28 13:54:54,685 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382889607758090320 responded with 429. Retrying in 0.34 seconds.
2025-06-28 13:54:55,697 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382881300494684192 responded with 429. Retrying in 0.32 seconds.
2025-06-28 13:54:56,673 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382880925079044116 responded with 429. Retrying in 0.35 seconds.
2025-06-28 13:54:57,777 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382880923099332738 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:54:58,882 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382880546144780370 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:54:59,927 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382878658020249660 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:00,905 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382874883133800601 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:01,876 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382872997274325144 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:02,807 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382872624983445545 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:03,766 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382872248993714218 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:04,701 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382872247546675363 responded with 429. Retrying in 0.32 seconds.
2025-06-28 13:55:05,694 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382872245294071828 responded with 429. Retrying in 0.34 seconds.
2025-06-28 13:55:06,675 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382872242752458786 responded with 429. Retrying in 0.35 seconds.
2025-06-28 13:55:07,627 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382872241355620502 responded with 429. Retrying in 0.39 seconds.
2025-06-28 13:55:08,739 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382824680557383732 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:09,697 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382751444691845151 responded with 429. Retrying in 0.33 seconds.
2025-06-28 13:55:10,716 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382749935505772739 responded with 429. Retrying in 0.31 seconds.
2025-06-28 13:55:11,794 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382703128150933504 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:20,791 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382147468459900938 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:22,423 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1382147464987017337 responded with 429. Retrying in 0.60 seconds.
2025-06-28 13:55:23,725 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381971556628172853 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:24,663 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381826224518725632 responded with 429. Retrying in 0.36 seconds.
2025-06-28 13:55:25,662 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381790742388805794 responded with 429. Retrying in 0.36 seconds.
2025-06-28 13:55:26,702 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381785090635469042 responded with 429. Retrying in 0.32 seconds.
2025-06-28 13:55:27,795 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381785088475402333 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:28,765 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381785086797811835 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:29,674 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381785084541010104 responded with 429. Retrying in 0.34 seconds.
2025-06-28 13:55:30,687 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381785082901037096 responded with 429. Retrying in 0.33 seconds.
2025-06-28 13:55:31,768 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381780548481187850 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:32,690 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381776018808766556 responded with 429. Retrying in 0.33 seconds.
2025-06-28 13:55:33,746 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381769602618622046 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:34,736 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381759785493594135 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:35,665 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381759408677326848 responded with 429. Retrying in 0.35 seconds.
2025-06-28 13:55:36,687 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381569532212285490 responded with 429. Retrying in 0.34 seconds.
2025-06-28 13:55:37,685 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381565757791539385 responded with 429. Retrying in 0.33 seconds.
2025-06-28 13:55:38,657 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381428895965909064 responded with 429. Retrying in 0.36 seconds.
2025-06-28 13:55:39,781 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381341920038621248 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:46,004 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381200249661685860 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:46,969 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1381060245648048268 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:47,990 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1380909251068956682 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:48,939 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1380905478061686784 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:49,950 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1380905476535091253 responded with 429. Retrying in 0.30 seconds.
2025-06-28 13:55:50,998 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 181 messages from #🚨-alerts (keep_pinned: True)
2025-06-28 13:58:42,467 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-06-29 00:16:35,754 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 618, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/discord/ext/tasks/__init__.py", line 246, in _loop
    await self.coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 355, in watchlist_updater
    ticker = exchange.fetch_ticker(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4105, in fetch_ticker
    response = self.fapiPublicGetTicker24hr(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 746, in send
    r.content
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 902, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 820, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 572, in stream
    for line in self.read_chunked(amt, decode_content=decode_content):
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 764, in read_chunked
    self._update_chunk_length()
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 694, in _update_chunk_length
    line = self._fp.fp.readline()
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-29 00:16:42,985 - __main__ - ERROR - Error fetching ticker for LINKUSDT: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=LINKUSDT
2025-06-29 09:47:50,095 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388671235386507325 responded with 429. Retrying in 0.38 seconds.
2025-06-29 09:47:53,256 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388599519448535170 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:47:54,138 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388595739176992819 responded with 429. Retrying in 0.34 seconds.
2025-06-29 09:47:55,111 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388593227342286895 responded with 429. Retrying in 0.37 seconds.
2025-06-29 09:47:56,190 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388590708306219170 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:47:57,377 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388588189035270336 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:47:58,371 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388585676378996905 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:47:59,468 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388580642828062845 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:00,392 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388575607427825784 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:02,026 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388541632818712586 responded with 429. Retrying in 0.46 seconds.
2025-06-29 09:48:03,207 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388536603147763815 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:04,117 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388534083931344927 responded with 429. Retrying in 0.36 seconds.
2025-06-29 09:48:05,070 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388527791514845315 responded with 429. Retrying in 0.41 seconds.
2025-06-29 09:48:06,169 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388520243592433724 responded with 429. Retrying in 0.31 seconds.
2025-06-29 09:48:07,190 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388520241893740738 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:08,067 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388517744231841919 responded with 429. Retrying in 0.41 seconds.
2025-06-29 09:48:09,202 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388517740800643093 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:10,263 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388517740259709008 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:11,214 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388513590125924406 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:12,103 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388512334061895801 responded with 429. Retrying in 0.37 seconds.
2025-06-29 09:48:13,196 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388509837834453054 responded with 429. Retrying in 0.31 seconds.
2025-06-29 09:48:14,125 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388509835624054996 responded with 429. Retrying in 0.35 seconds.
2025-06-29 09:48:15,154 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388509834420424826 responded with 429. Retrying in 0.32 seconds.
2025-06-29 09:48:16,092 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388508925112090665 responded with 429. Retrying in 0.39 seconds.
2025-06-29 09:48:17,133 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388508925078405212 responded with 429. Retrying in 0.35 seconds.
2025-06-29 09:48:18,133 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388508923258212384 responded with 429. Retrying in 0.35 seconds.
2025-06-29 09:48:19,161 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388497500389900288 responded with 429. Retrying in 0.33 seconds.
2025-06-29 09:48:20,132 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388494987167142079 responded with 429. Retrying in 0.35 seconds.
2025-06-29 09:48:21,148 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388469818423906456 responded with 429. Retrying in 0.33 seconds.
2025-06-29 09:48:22,088 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388467302139301949 responded with 429. Retrying in 0.40 seconds.
2025-06-29 09:48:23,133 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388419490903101531 responded with 429. Retrying in 0.35 seconds.
2025-06-29 09:48:24,183 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388405646159773706 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:25,084 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388390546581622866 responded with 429. Retrying in 0.39 seconds.
2025-06-29 09:48:26,091 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388375446327922738 responded with 429. Retrying in 0.38 seconds.
2025-06-29 09:48:27,106 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388342730790408394 responded with 429. Retrying in 0.38 seconds.
2025-06-29 09:48:28,088 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388320082463162450 responded with 429. Retrying in 0.39 seconds.
2025-06-29 09:48:29,127 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388310018285240390 responded with 429. Retrying in 0.35 seconds.
2025-06-29 09:48:30,156 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388308764293201991 responded with 429. Retrying in 0.33 seconds.
2025-06-29 09:48:31,150 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388268495254327399 responded with 429. Retrying in 0.33 seconds.
2025-06-29 09:48:32,102 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388199286747041972 responded with 429. Retrying in 0.38 seconds.
2025-06-29 09:48:33,150 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388196773641715733 responded with 429. Retrying in 0.34 seconds.
2025-06-29 09:48:34,109 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388191736043536454 responded with 429. Retrying in 0.37 seconds.
2025-06-29 09:48:35,071 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388189218907357275 responded with 429. Retrying in 0.41 seconds.
2025-06-29 09:48:36,110 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388132598756147292 responded with 429. Retrying in 0.37 seconds.
2025-06-29 09:48:37,159 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388118754923122771 responded with 429. Retrying in 0.32 seconds.
2025-06-29 09:48:38,100 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388084781492998194 responded with 429. Retrying in 0.38 seconds.
2025-06-29 09:48:39,172 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388079751045976075 responded with 429. Retrying in 0.31 seconds.
2025-06-29 09:48:40,149 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388063390282289274 responded with 429. Retrying in 0.33 seconds.
2025-06-29 09:48:41,148 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388060877646856253 responded with 429. Retrying in 0.34 seconds.
2025-06-29 09:48:45,416 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388005509067440201 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:46,359 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388002992032186400 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:47,288 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388000479799414865 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:48,355 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388000477207335003 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:49,295 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388000477127381143 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:50,305 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387999851941462196 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:51,232 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387999847293911212 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:52,175 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387999847088656545 responded with 429. Retrying in 0.31 seconds.
2025-06-29 09:48:53,214 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387998465614942280 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:54,179 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387998465472204910 responded with 429. Retrying in 0.31 seconds.
2025-06-29 09:48:55,229 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387998462800564356 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:48:56,184 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387995421070200845 responded with 429. Retrying in 0.31 seconds.
2025-06-29 09:48:57,171 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387995420382203987 responded with 429. Retrying in 0.32 seconds.
2025-06-29 09:48:58,135 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387995418503417950 responded with 429. Retrying in 0.35 seconds.
2025-06-29 09:48:59,139 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382148217151553610 responded with 429. Retrying in 0.35 seconds.
2025-06-29 09:49:00,127 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382129365751693313 responded with 429. Retrying in 0.36 seconds.
2025-06-29 09:49:01,144 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382124310810067246 responded with 429. Retrying in 0.34 seconds.
2025-06-29 09:49:02,127 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382076509258252288 responded with 429. Retrying in 0.36 seconds.
2025-06-29 09:49:03,195 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382071462877335663 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:04,127 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382031222234026106 responded with 429. Retrying in 0.37 seconds.
2025-06-29 09:49:06,109 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382014840192897064 responded with 429. Retrying in 0.38 seconds.
2025-06-29 09:49:07,164 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382012328173895812 responded with 429. Retrying in 0.32 seconds.
2025-06-29 09:49:08,214 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381988415469522957 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:09,327 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381985913864716348 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:10,339 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381983383374135347 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:11,359 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381978362230079560 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:13,007 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381975833450774528 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:38,611 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 618, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/discord/ext/tasks/__init__.py", line 246, in _loop
    await self.coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 385, in watchlist_updater
    embed_content = format_watchlist_message(watchlist_data)
  File "/root/chartfix/utils/ui_components.py", line 370, in format_watchlist_message
    return format_price_message(prices)
  File "/root/chartfix/utils/ui_components.py", line 258, in format_price_message
    volume = fetch_volume_data(symbol)
  File "/root/chartfix/utils/ui_components.py", line 391, in fetch_volume_data
    ticker = exchange.fetch_ticker(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4105, in fetch_ticker
    response = self.fapiPublicGetTicker24hr(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 746, in send
    r.content
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 902, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 820, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 572, in stream
    for line in self.read_chunked(amt, decode_content=decode_content):
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 764, in read_chunked
    self._update_chunk_length()
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 694, in _update_chunk_length
    line = self._fp.fp.readline()
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-29 09:49:45,188 - utils.ui_components - ERROR - Lỗi khi lấy khối lượng giao dịch cho XRPUSDT: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=XRPUSDT
2025-06-29 09:49:48,614 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 618, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 309, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4374, in fetch2
    self.throttle(cost)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 463, in throttle
    time.sleep(delay / 1000.0)

2025-06-29 09:49:49,522 - handlers.discord.market.market_commands - WARNING - Interaction already expired for price command by fantastic_beagle_96376
2025-06-29 09:49:49,550 - handlers.discord.market.market_commands - WARNING - Interaction already expired for price command by fantastic_beagle_96376
2025-06-29 09:49:51,972 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381915434160488510 responded with 429. Retrying in 0.32 seconds.
2025-06-29 09:49:52,963 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381895315925237800 responded with 429. Retrying in 0.34 seconds.
2025-06-29 09:49:54,036 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381844970893479979 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:55,017 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381842467908747374 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:56,029 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381837420793827409 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:57,005 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381828626487443497 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:57,912 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381818546220302466 responded with 429. Retrying in 0.37 seconds.
2025-06-29 09:49:58,986 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381810996716376227 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:49:59,954 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381803447271035034 responded with 429. Retrying in 0.33 seconds.
2025-06-29 09:50:01,109 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381797169216094218 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:02,101 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381792123497742477 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:03,242 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381789619686740049 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:04,688 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381785844779843665 responded with 429. Retrying in 0.61 seconds.
2025-06-29 09:50:06,076 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381784574350327808 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:07,105 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381782069641871421 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:08,074 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381780797861925026 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:09,063 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381777022870884433 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:10,125 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381770745008361675 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:11,263 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381769472548929570 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:12,157 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381766969560268810 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:13,160 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381764440000696462 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:14,154 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381761927822180474 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:15,096 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381758148683108433 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:16,081 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381750598763942070 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:17,069 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381748095984865311 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:17,965 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381739274029764913 responded with 429. Retrying in 0.31 seconds.
2025-06-29 09:50:18,992 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381721660729720964 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:19,938 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381671326523195532 responded with 429. Retrying in 0.34 seconds.
2025-06-29 09:50:20,910 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381653723893338172 responded with 429. Retrying in 0.37 seconds.
2025-06-29 09:50:21,950 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381618479375056896 responded with 429. Retrying in 0.33 seconds.
2025-06-29 09:50:23,091 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381595829827276904 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:24,030 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381584504774463588 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:25,001 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381582000833433734 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:25,955 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381580729523240960 responded with 429. Retrying in 0.32 seconds.
2025-06-29 09:50:26,875 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381576954406244362 responded with 429. Retrying in 0.40 seconds.
2025-06-29 09:50:27,943 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381573180941930667 responded with 429. Retrying in 0.34 seconds.
2025-06-29 09:50:28,984 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381570677148221495 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:30,034 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381566904296144998 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:31,000 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381559352694931579 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:32,066 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381531656762163240 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:33,133 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381425959600652419 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:34,233 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381423455467077723 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:35,945 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381346687200792738 responded with 429. Retrying in 0.34 seconds.
2025-06-29 09:50:36,900 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381330329394216990 responded with 429. Retrying in 0.38 seconds.
2025-06-29 09:50:37,904 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381313984707166269 responded with 429. Retrying in 0.38 seconds.
2025-06-29 09:50:38,961 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381308938364125286 responded with 429. Retrying in 0.33 seconds.
2025-06-29 09:50:39,938 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381272468664090655 responded with 429. Retrying in 0.34 seconds.
2025-06-29 09:50:40,975 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381240996704026816 responded with 429. Retrying in 0.30 seconds.
2025-06-29 09:50:41,877 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381238488216899734 responded with 429. Retrying in 0.40 seconds.
2025-06-29 09:50:47,752 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 157 messages from #time-trade (keep_pinned: True)
2025-06-29 12:19:46,909 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 20 messages from #📝-bot-logs (keep_pinned: True)
2025-06-29 12:20:34,830 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1386697177400868979 responded with 429. Retrying in 0.47 seconds.
2025-06-29 12:20:35,984 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1386495977304752321 responded with 429. Retrying in 0.31 seconds.
2025-06-29 12:20:36,936 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1386133588612878406 responded with 429. Retrying in 0.36 seconds.
2025-06-29 12:20:37,939 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1385771202131263659 responded with 429. Retrying in 0.36 seconds.
2025-06-29 12:20:39,010 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1385408812969889886 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:39,918 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1385046425452281967 responded with 429. Retrying in 0.38 seconds.
2025-06-29 12:20:40,926 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1384684035694919812 responded with 429. Retrying in 0.38 seconds.
2025-06-29 12:20:41,964 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1384503752467419189 responded with 429. Retrying in 0.33 seconds.
2025-06-29 12:20:46,646 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1384413144301965354 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:49,192 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1384352759431565434 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:50,229 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1384321647871524904 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:51,151 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1383959260152598710 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:52,161 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1383596874937663528 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:53,175 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1383234487386636340 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:54,119 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1383076839592955984 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:55,090 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1383076836191375360 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:56,071 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1382872100339191811 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:56,991 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1382691804322725908 responded with 429. Retrying in 0.31 seconds.
2025-06-29 12:20:58,024 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1382691800187277423 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:20:59,112 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1382691798652026910 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:21:00,112 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1382509708635803648 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:21:01,047 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1382329427106726041 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:21:02,015 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1382329425676341339 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:21:02,958 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1382329417786855517 responded with 429. Retrying in 0.34 seconds.
2025-06-29 12:21:03,962 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1382147320774266941 responded with 429. Retrying in 0.34 seconds.
2025-06-29 12:21:04,946 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1381784932740890734 responded with 429. Retrying in 0.35 seconds.
2025-06-29 12:21:06,006 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1381422546028597298 responded with 429. Retrying in 0.30 seconds.
2025-06-29 12:21:06,968 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376253745536565280/messages/1381060157777252363 responded with 429. Retrying in 0.33 seconds.
2025-06-29 12:21:08,259 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 40 messages from #economic-calendar (keep_pinned: True)
2025-07-01 07:20:27,796 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b843255b0ac7fe17307275f382d554d617c98231c51cf85be105acb5a2c75d1f
2025-07-01 07:20:27,799 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b843255b0ac7fe17307275f382d554d617c98231c51cf85be105acb5a2c75d1f (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b843255b0ac7fe17307275f382d554d617c98231c51cf85be105acb5a2c75d1f)
2025-07-01 07:24:49,656 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9f75bdceb61eccb63048bb74f36d9868fa79995c0f2eb000e384d1aad4173315
2025-07-01 07:24:49,663 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9f75bdceb61eccb63048bb74f36d9868fa79995c0f2eb000e384d1aad4173315 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9f75bdceb61eccb63048bb74f36d9868fa79995c0f2eb000e384d1aad4173315)
2025-07-02 05:37:19,824 - services.market.market_service - ERROR - [MARKET-SERVICE] CCXT error fetching prices for ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'NEARUSDT', 'LINKUSDT', 'ENAUSDT', 'DOGEUSDT', 'XRPUSDT', 'WLDUSDT']: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr
2025-07-02 05:37:19,830 - services.market.market_service - WARNING - No price data received for watchlist symbols
2025-07-02 14:03:08,964 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 9 messages from #📈-market-data (keep_pinned: False)
2025-07-02 17:10:49,590 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 120 messages from #time-trade (keep_pinned: True)
2025-07-03 13:10:59,095 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6c3fbec5c8c43fdb7940b528656d37f011c7a95ee73ba48273fddfb6ac22b6ca
2025-07-03 13:10:59,099 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6c3fbec5c8c43fdb7940b528656d37f011c7a95ee73ba48273fddfb6ac22b6ca (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6c3fbec5c8c43fdb7940b528656d37f011c7a95ee73ba48273fddfb6ac22b6ca)
2025-07-03 18:17:53,093 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=2c03985ea48122c13ed6ec909cded1722bd29218b00ce008c6d9685c98baf91d
2025-07-03 18:17:53,099 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=2c03985ea48122c13ed6ec909cded1722bd29218b00ce008c6d9685c98baf91d (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=2c03985ea48122c13ed6ec909cded1722bd29218b00ce008c6d9685c98baf91d)
2025-07-03 23:14:30,321 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 639, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 66, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-07-03 23:14:40,324 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 639, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 66, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-07-03 23:14:48,786 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=815b8b77f5c0eda20efc42d930932534d7a58e5afa6df62db9853ec80325eddf
2025-07-03 23:14:48,788 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=815b8b77f5c0eda20efc42d930932534d7a58e5afa6df62db9853ec80325eddf (Caused by: RequestTimeout: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=815b8b77f5c0eda20efc42d930932534d7a58e5afa6df62db9853ec80325eddf)
2025-07-03 23:14:50,326 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 639, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 188, in sync_wrapper
    time.sleep(sleep_time)

2025-07-04 00:02:43,526 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-04 00:07:36,501 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-04 07:25:33,092 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6f2772cca30c299a6fafa7cfd2e05c1996b44e0fa2c062ef4f56af74ef9c6ed2
2025-07-04 07:25:33,097 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6f2772cca30c299a6fafa7cfd2e05c1996b44e0fa2c062ef4f56af74ef9c6ed2 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6f2772cca30c299a6fafa7cfd2e05c1996b44e0fa2c062ef4f56af74ef9c6ed2)
2025-07-04 07:43:21,511 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f1bd7b468499e457d7a097ff815388cc1123326048db6dd8e715504c6f6453a3
2025-07-04 07:43:21,516 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f1bd7b468499e457d7a097ff815388cc1123326048db6dd8e715504c6f6453a3 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f1bd7b468499e457d7a097ff815388cc1123326048db6dd8e715504c6f6453a3)
2025-07-04 16:54:09,044 - utils.ui_components - ERROR - Lỗi khi lấy khối lượng giao dịch cho XRPUSDT: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=XRPUSDT
2025-07-04 16:54:09,721 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 639, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/discord/ext/tasks/__init__.py", line 246, in _loop
    await self.coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 406, in watchlist_updater
    embed_content = format_watchlist_message(watchlist_data)
  File "/root/chartfix/utils/ui_components.py", line 355, in format_watchlist_message
    return format_price_message(prices)
  File "/root/chartfix/utils/ui_components.py", line 291, in format_price_message
    df = market_service._fetch_ohlcv_data(sym, timeframe='1d', limit=2)
  File "/root/chartfix/services/market/market_service.py", line 614, in _fetch_ohlcv_data
    ohlcv = exchange.fetch_ohlcv(formatted_symbol, timeframe, limit=limit)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4501, in fetch_ohlcv
    response = self.fapiPublicGetKlines(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-07-05 00:02:40,990 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-05 00:07:36,603 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-05 01:19:05,502 - services.market.market_service - ERROR - [MARKET-SERVICE] CCXT error fetching prices for ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'NEARUSDT', 'LINKUSDT', 'ENAUSDT', 'DOGEUSDT', 'XRPUSDT', 'WLDUSDT']: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr
2025-07-05 01:19:05,505 - services.market.market_service - WARNING - No price data received for watchlist symbols
2025-07-05 08:37:12,936 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 31 messages from #trade (keep_pinned: False)
2025-07-05 08:38:11,687 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 96 messages from #time-trade (keep_pinned: True)
2025-07-05 12:44:23,854 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-05 12:46:57,409 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #📈-market-data (keep_pinned: False)
2025-07-05 12:49:17,345 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-05 12:59:04,370 - handlers.discord.market.market_commands - ERROR - Error fetching spot/futures data for BTCUSDT: binance does not have market symbol BTCUSDT/USDT:USDT
2025-07-05 12:59:49,618 - handlers.discord.market.market_commands - ERROR - Error fetching spot/futures data for BTCUSDT: binance does not have market symbol BTCUSDTUSDT
