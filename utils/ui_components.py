import logging
import datetime
from datetime import timezone
from typing import Dict, Union, Optional
from services.core.symbol_service import normalize_symbol, is_valid_symbol
from utils.config import get_watchlist_symbols
from utils.constants import (
    EMOJI,
    DISCORD_FORMATTING,
    PRICE_FORMAT_RULES,
    VOLUME_FORMAT_RULES,
    MESSAGE_TEMPLATES
)

logger = logging.getLogger(__name__)

VALID_TIMEFRAMES = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']

def format_price_display(price: float) -> str:
    if price < 0.001:
        return f"${price:.2e}".replace('e-0', ' × 10^-').replace('e-', ' × 10^-')
    elif price < 1:
        formatted = f"${price:.4f}".rstrip('0').rstrip('.')
        if formatted == "$":
            formatted = "$0"
        return formatted
    elif price < 1000:
        return f"${price:.2f}"
    else:
        return f"${price:,.2f}"

def format_price_enhanced(price: float) -> str:
    if price <= 0:
        return "$0.00"

    # Sort rules by threshold in descending order to check largest first
    sorted_rules = sorted(PRICE_FORMAT_RULES.items(), key=lambda x: x[1]['threshold'], reverse=True)

    for rule_name, rule in sorted_rules:
        if price >= rule['threshold']:
            decimals = rule['decimals']
            prefix = rule['prefix']
            use_comma = rule.get('use_comma', False)

            if use_comma:
                formatted = f"{prefix}{price:,.{decimals}f}"
            else:
                formatted = f"{prefix}{price:.{decimals}f}"

            # For prices >= $1000, no decimals needed (already handled by decimals=0)
            # For prices >= $100 but < $1000, keep 2 decimals but remove trailing zeros
            # For smaller prices, keep the specified decimals but remove trailing zeros
            if '.' in formatted and price >= 100 and price < 1000:
                formatted = formatted.rstrip('0')
                if formatted.endswith('.'):
                    formatted += '00'
            elif '.' in formatted and price >= 1 and price < 100:
                formatted = formatted.rstrip('0')
                if formatted.endswith('.'):
                    formatted += '00'
            elif '.' in formatted and price < 1:
                # For small prices, remove trailing zeros but keep meaningful precision
                formatted = formatted.rstrip('0')
                if formatted.endswith('.'):
                    formatted += '0'

            return formatted

    return f"${price:.8f}"

def format_volume_enhanced(volume: float) -> str:
    if volume <= 0:
        return "0"

    for rule_name, rule in VOLUME_FORMAT_RULES.items():
        if volume >= rule['threshold']:
            decimals = rule['decimals']
            suffix = rule['suffix']

            if suffix:
                return f"{volume/rule['threshold']:.{decimals}f}{suffix}"

    return f"{volume:.0f}"

def format_percentage_enhanced(percentage: float, show_sign: bool = True, include_emoji: bool = True) -> str:
    if percentage > 0:
        emoji = EMOJI['green_circle'] if include_emoji else ""
        sign = "+" if show_sign else ""
    elif percentage < 0:
        emoji = EMOJI['red_circle'] if include_emoji else ""
        sign = ""
    else:
        emoji = EMOJI['neutral'] if include_emoji else ""
        sign = ""

    if include_emoji:
        return f"{emoji}{sign}{percentage:.2f}%"
    else:
        return f"{sign}{percentage:.2f}%"

def format_mobile_friendly_text(text: str, max_width: int = None) -> str:
    if max_width is None:
        max_width = DISCORD_FORMATTING['limits']['line_length']

    words = text.split()
    lines = []
    current_line = []
    current_length = 0

    for word in words:
        if current_length + len(word) + 1 <= max_width:
            current_line.append(word)
            current_length += len(word) + 1
        else:
            if current_line:
                lines.append(' '.join(current_line))
            current_line = [word]
            current_length = len(word)

    if current_line:
        lines.append(' '.join(current_line))

    return '\n'.join(lines)

def create_mobile_optimized_table(data: Dict[str, str], max_width: int = None) -> str:
    if max_width is None:
        max_width = DISCORD_FORMATTING['limits']['code_block_width']

    lines = []
    for key, value in data.items():
        line = f"{key}: {value}"
        if len(line) > max_width:
            lines.append(f"{key}:")
            lines.append(f"  {value}")
        else:
            lines.append(line)

    return '\n'.join(lines)

def format_price_message(prices: Dict[str, Union[float, str]]) -> str:
    logger.info(f"[FORMAT-MESSAGE] format_price_message called with {len(prices)} prices")
    logger.debug(f"[FORMAT-MESSAGE] Input prices: {prices}")

    watchlist_symbols = get_watchlist_symbols()
    logger.info(f"[FORMAT-MESSAGE] Watchlist symbols from config: {watchlist_symbols}")

    if not watchlist_symbols:
        logger.warning(f"[FORMAT-MESSAGE] No watchlist symbols configured")
        return "⚠️ Chưa cấu hình danh sách theo dõi trong config.yaml"

    lines = []
    lines.append(f"{EMOJI['rocket']} *CRYPTO WATCHLIST* {EMOJI['chart']}")
    lines.append("```")
    lines.append("TOKEN  PRICE    CHG    VOL")
    lines.append("------------------------------")

    if not prices:
        logger.warning(f"[FORMAT-MESSAGE] No prices provided, attempting fallback fetch")
        from services.market.market_service import get_market_service
        market_service = get_market_service()

        all_prices = market_service.get_all_prices()
        logger.info(f"[FORMAT-MESSAGE] Fallback all_prices: {all_prices}")
        prices = {}

        for symbol in watchlist_symbols:
            if symbol in all_prices:
                prices[symbol] = all_prices[symbol]
                logger.debug(f"[FORMAT-MESSAGE] Fallback found price for {symbol}: {all_prices[symbol]}")
            else:
                prices[symbol] = 0
                logger.warning(f"[FORMAT-MESSAGE] Fallback no price for {symbol}, setting to 0")

        logger.info(f"[FORMAT-MESSAGE] Fallback final prices: {prices}")
        if not prices:
            logger.error(f"[FORMAT-MESSAGE] Fallback failed - no prices available")
            return "⚠️ Không thể lấy dữ liệu giá từ Binance"

    normalized_symbols = []
    for symbol in watchlist_symbols:
        if isinstance(symbol, str) and symbol:
            normalized = normalize_symbol(symbol)
            normalized_symbols.append(normalized)
            logger.debug(f"[FORMAT-MESSAGE] Normalized {symbol} -> {normalized}")

    logger.info(f"[FORMAT-MESSAGE] Normalized symbols: {normalized_symbols}")

    valid_symbols = []
    error_symbols = []

    for s in normalized_symbols:
        if s in prices and isinstance(prices[s], float):
            valid_symbols.append(s)
            logger.debug(f"[FORMAT-MESSAGE] Valid symbol: {s} = {prices[s]}")
        else:
            error_symbols.append(s)
            logger.warning(f"[FORMAT-MESSAGE] Error symbol: {s} (in_prices: {s in prices}, type: {type(prices.get(s))})")

    logger.info(f"[FORMAT-MESSAGE] Valid symbols: {valid_symbols}")
    logger.info(f"[FORMAT-MESSAGE] Error symbols: {error_symbols}")

    for symbol in valid_symbols:
        price = prices[symbol]

        price_str = format_price_display(price)

        try:
            # Use unified percentage calculation service instead of manual OHLCV calculation
            from services.market.market_service import get_market_service, get_binance_futures_exchange
            from services.market.percentage_calculation_service import get_percentage_service
            from services.core.symbol_service import format_symbol_for_exchange

            percentage_service = get_percentage_service()

            # Get 24h rolling change from Binance (consistent with watchlist and alerts)
            try:
                exchange = get_binance_futures_exchange()
                formatted_symbol = format_symbol_for_exchange(symbol)
                ticker = exchange.fetch_ticker(formatted_symbol)

                percentage_result = percentage_service.extract_binance_percentage(ticker)

                if percentage_result.is_valid:
                    change_str = percentage_service.format_percentage_display(
                        percentage_result,
                        include_sign=True,
                        include_emoji=True
                    )
                    logger.info(f"Sử dụng 24h rolling change cho {symbol}: {percentage_result.value}% (Binance API)")
                else:
                    change_str = "N/A"
                    logger.warning(f"Không thể lấy 24h rolling change cho {symbol}")

            except Exception as ticker_error:
                logger.error(f"Lỗi khi lấy ticker data cho {symbol}: {ticker_error}")
                change_str = "N/A"

        except Exception as e:
            logger.error(f"Lỗi khi tính toán biến động giá cho {symbol}: {e}")
            change_str = "N/A"

        try:
            volume = fetch_volume_data(symbol)
            if volume > 0:
                if volume < 1_000_000:
                    volume_str = f"{volume/1000:.1f}K"
                elif volume < 1_000_000_000:
                    volume_str = f"{volume/1_000_000:.1f}M"
                else:
                    volume_str = f"{volume/1_000_000_000:.1f}B"
            else:
                volume_str = "-"
        except Exception:
            volume_str = "-"

        token_display = symbol.replace('USDT', '')[:6]
        token_pad = ' ' * max(0, 6 - len(token_display))
        price_pad = ' ' * max(0, 9 - len(price_str))
        change_pad = ' ' * max(0, 7 - len(change_str))

        lines.append(f"{token_display}{token_pad}{price_str}{price_pad}{change_str}{change_pad}{volume_str}")

    if error_symbols:
        lines.append("------------------------------")
        lines.append("SYMBOL LỖI:")

        for symbol in error_symbols:
            token_display = symbol.replace('USDT', '')[:6]
            token_pad = ' ' * max(0, 6 - len(token_display))
            error_msg = prices.get(symbol, "Unknown error") if isinstance(prices.get(symbol), str) else "Lỗi giá"
            lines.append(f"{token_display}{token_pad}{error_msg}...")

    lines.append("```")

    # Thêm thống kê thị trường dựa trên giá mở cửa nến ngày
    coins_up = 0
    coins_down = 0

    try:
        from services.market.market_service import get_market_service
        market_service = get_market_service()

        # Lấy thời gian hiện tại theo UTC
        now = datetime.datetime.now(timezone.utc)

        # Lấy dữ liệu OHLCV cho tất cả các symbol cùng một lúc
        ohlcv_data = {}
        for sym in valid_symbols:
            try:
                # Lấy 2 nến gần nhất
                df = market_service._fetch_ohlcv_data(sym, timeframe='1d', limit=2)
                if not df.empty:
                    ohlcv_data[sym] = df
            except Exception as e:
                logger.debug(f"Không thể lấy dữ liệu OHLCV cho {sym}: {e}")

        # Tính toán thống kê thị trường
        for sym in valid_symbols:
            try:
                daily_open = None

                # Thử lấy từ dữ liệu OHLCV
                if sym in ohlcv_data and not ohlcv_data[sym].empty:
                    df = ohlcv_data[sym]

                    # Kiểm tra xem nến đầu tiên có phải là nến của ngày hiện tại không
                    first_candle_time = df['timestamp'].iloc[0]
                    if first_candle_time.tzinfo is None:
                        first_candle_time = first_candle_time.replace(tzinfo=timezone.utc)

                    if first_candle_time.date() == now.date():
                        # Nến đầu tiên là nến ngày hiện tại
                        daily_open = df['open'].iloc[0]
                        logger.info(f"Thống kê thị trường: Sử dụng giá mở cửa từ nến ngày hiện tại cho {sym}: {daily_open} (API OHLCV)")
                    elif len(df) > 1:
                        # Nến thứ hai là nến ngày hiện tại
                        daily_open = df['open'].iloc[1]
                        logger.info(f"Thống kê thị trường: Sử dụng giá mở cửa từ nến thứ hai cho {sym}: {daily_open} (API OHLCV)")

                # No fallback data available if OHLCV fails
                # daily_open remains None if not found in OHLCV data

                # So sánh giá hiện tại với giá mở cửa nến ngày
                if daily_open is not None and daily_open > 0:
                    if prices[sym] > daily_open:
                        coins_up += 1
                    elif prices[sym] < daily_open:
                        coins_down += 1
            except Exception as e:
                logger.debug(f"Không thể tính toán thống kê thị trường cho {sym}: {e}")
    except Exception as e:
        logger.error(f"Lỗi khi tính toán tổng quan thị trường: {e}")

    lines.append(f"*Tổng quan thị trường:* {EMOJI['small_up']} {coins_up} tăng | {EMOJI['small_down']} {coins_down} giảm")

    # Tạo final message
    final_message = "\n".join(lines)
    logger.info(f"[FORMAT-MESSAGE] Final message created, length: {len(final_message)} chars")
    logger.debug(f"[FORMAT-MESSAGE] Final message preview: {final_message[:200]}...")

    # Không thêm hashtags cho các symbols nữa
    return final_message

def format_watchlist_message(watchlist_data: dict) -> str:
    """Format watchlist data for Discord embed description"""
    try:
        if not watchlist_data.get('success', False):
            return "❌ Không thể lấy dữ liệu watchlist"

        prices = watchlist_data.get('prices', {})
        if not prices:
            return "❌ Không có dữ liệu giá"

        # Use the existing format_price_message function
        return format_price_message(prices)

    except Exception as e:
        logger.error(f"Error formatting watchlist message: {e}")
        return "❌ Lỗi khi định dạng dữ liệu watchlist"

def fetch_volume_data(symbol: str) -> float:
    """Lấy khối lượng giao dịch 24h của một symbol."""
    try:
        from services.market.market_service import get_binance_futures_exchange
        from services.core.symbol_service import format_symbol_for_exchange

        # Chuẩn hóa và kiểm tra symbol
        symbol = normalize_symbol(symbol)
        if not is_valid_symbol(symbol):
            logger.warning(f"Symbol không hợp lệ: {symbol}")
            return 0

        # Get volume from exchange
        exchange = get_binance_futures_exchange()
        formatted_symbol = format_symbol_for_exchange(symbol)
        ticker = exchange.fetch_ticker(formatted_symbol)
        return ticker['quoteVolume'] if 'quoteVolume' in ticker else 0

    except Exception as e:
        logger.error(f"Lỗi khi lấy khối lượng giao dịch cho {symbol}: {str(e)}")
        return 0